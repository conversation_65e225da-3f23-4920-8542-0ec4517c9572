"""
MCQ Text Extractor Service

This service handles the extraction of text content from MCQ images.
It converts PDFs to images, extracts text from each image using LLM, and saves the result.
"""
import ast
import logging
import os
import asyncio
import re
import shutil
import traceback
import uuid
import json
import math
from typing import Dict, List, Optional

import config
from agents.utils.pdf_helpers import PDFImageConverter
from agents.core.extractor import ExtractorAgent
from agents.mcq_extractor import MCQExtractor
from agents.schemas.agent_prompts import mcq_text_extractor_prompt, mcq_parser_prompt, pre_process_prompt, \
    pre_process_explanation_prompt
from utils.s3_utils import upload_file_to_s3, read_file_from_s3
from sqlalchemy import text
from db_config.db import get_session, CONTENT_SCHEMA
from llm_manager.llm_factory import LLMFactory
from config import llm_config
from langchain_core.messages import HumanMessage



# Get logger instance
logger = logging.getLogger(__name__)

# Initialize LLM factory
llm_factory = LLMFactory(llm_config)



class MCQTextExtractorService:
    """
    Service for extracting text content from MCQ images.
    """

    def __init__(self, max_concurrent_extractions: int = None):
        """
        Initialize the MCQTextExtractorService.

        Args:
            max_concurrent_extractions: Maximum number of concurrent extractions to run in parallel
        """
        self.max_concurrent_extractions = max_concurrent_extractions or getattr(config, 'MAX_CONCURRENT_EXTRACTIONS', 2)
        self.pdf_zoom_factor = getattr(config, 'PDF_ZOOM_FACTOR', 1.5)
        self.mcq_batch_size = getattr(config, 'MCQ_BATCH_SIZE', 10)
        self.streaming_mode = getattr(config, 'MCQ_STREAMING_MODE', True)

        # Delay LLM initialization until first use to avoid blocking API response
        self.llm = None
        self._llm_initialized = False

        logger.info(f"MCQTextExtractorService initialized with max_concurrent_extractions={self.max_concurrent_extractions}, "
                   f"batch_size={self.mcq_batch_size}, streaming_mode={self.streaming_mode}")

    def _ensure_llm_initialized(self):
        """Ensure LLM is initialized (lazy initialization)."""
        if not self._llm_initialized:
            logger.info("Initializing LLM for MCQ text extraction...")
            self.llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=180)
            self._llm_initialized = True
            logger.info("LLM initialization completed")

    async def extract_text_from_resource(self, res_id: str, username: str = None, force_reextract: bool = False, total_questions: int = None, explanation_start_page: int = 5) -> Dict:
        """
        Extract text content from a PDF resource.

        Args:
            res_id: Resource ID
            username: Username of the user performing the extraction
            force_reextract: Whether to force re-extraction even if file exists
            total_questions: Total number of questions for MCQ parsing batches
            explanation_start_page: Page number from which explanations start (default: 5)

        Returns:
            Dict: Result of the extraction process
        """
        request_id = str(uuid.uuid4())
        logger.info(f"[REQUEST:{request_id}] Starting MCQ text extraction for resource ID {res_id}")



        try:
            # Step 1: Get resource details using MCQExtractor's method
            mcq_extractor = MCQExtractor()
            resource_details = await mcq_extractor.get_resource_details(res_id)

            if resource_details["status"] != "success":
                logger.error(f"[REQUEST:{request_id}] Error getting resource details: {resource_details['message']}")
                return resource_details

            resource_id = resource_details["resource_id"]
            chapter_id = resource_details["chapter_id"]
            book_id = resource_details["book_id"]
            file_path = resource_details["file_path"]

            logger.info(f"[REQUEST:{request_id}] Resource details - ID: {resource_id}, Chapter: {chapter_id}, Book: {book_id}")

            # Step 1.5: Check if text file already exists in S3 (unless force_reextract is True)
            if not force_reextract:
                existing_text_result = await self.get_extracted_text(chapter_id, res_id)
                if existing_text_result["status"] == "success":
                    logger.info(f"[REQUEST:{request_id}] Text file already exists, returning existing content")
                    return {
                        "status": "success",
                        "message": "Text file already exists, no extraction needed",
                        "s3_path": existing_text_result["s3_path"],
                        "total_images": 0,
                        "text_files_created": 0,
                        "chapter_id": chapter_id,
                        "resource_id": resource_id,
                        "already_existed": True
                    }
            else:
                logger.info(f"[REQUEST:{request_id}] Force re-extraction requested, proceeding with extraction")

            # Step 2: Convert PDF to column images
            logger.info(f"[REQUEST:{request_id}] Starting PDF to image conversion")

            pdf_converter = PDFImageConverter()
            conversion_result = pdf_converter.convert_and_upload(
                pdf_path=file_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                zoom=self.pdf_zoom_factor
            )

            if conversion_result["status"] != "success":
                logger.error(f"[REQUEST:{request_id}] PDF conversion failed: {conversion_result['message']}")
                return {"status": "error", "message": conversion_result["message"]}

            # Get the column image URLs
            col_img_urls = conversion_result.get("cropped_image_urls", [])
            if not col_img_urls:
                logger.error(f"[REQUEST:{request_id}] No column images found")
                return {"status": "error", "message": "No column images found"}

            logger.info(f"[REQUEST:{request_id}] Found {len(col_img_urls)} column images")

            # Step 3: Extract text from images in parallel
            text_files = await self._extract_text_parallel(col_img_urls, request_id, resource_id, chapter_id, book_id)

            if not text_files:
                logger.error(f"[REQUEST:{request_id}] No text files were created")
                return {"status": "error", "message": "No text files were created"}



            # Step 4: Merge all text files into one combined file
            combined_file_path = await self._merge_text_files(text_files, chapter_id, resource_id, request_id)

            if not combined_file_path:
                logger.error(f"[REQUEST:{request_id}] Failed to create combined text file")
                return {"status": "error", "message": "Failed to create combined text file"}

            # Step 5: Upload combined file to S3
            s3_upload_result = upload_file_to_s3(
                local_file_path=combined_file_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                file_name=f"{chapter_id}_{resource_id}.txt",
                is_quiz_image=False
            )

            if not s3_upload_result:
                logger.error(f"[REQUEST:{request_id}] Failed to upload combined file to S3")
                return {"status": "error", "message": "Failed to upload combined file to S3"}



            # Step 6: MCQ parsing and processing
            images_from_content = self.get_quiz_images(res_id, chapter_id, book_id, conversion_result.get("image_urls", []), explanation_start_page)

            json_s3_path = None
            if total_questions:
                logger.info(f"[REQUEST:{request_id}] Starting MCQ parsing for {total_questions} questions")

                # Process MCQ parsing with streaming approach
                from utils.s3_utils import get_s3_path
                full_s3_path = get_s3_path(s3_upload_result)
                logger.info(f"[REQUEST:{request_id}] Processing MCQ parsing with streaming approach from: {full_s3_path}")

                try:
                    # Use streaming approach to process MCQ parsing
                    json_s3_path = await self._process_mcq_parsing_batches(
                        full_s3_path, total_questions, resource_id, chapter_id,
                        book_id, request_id, combined_file_path, username, images_from_content
                    )

                    if json_s3_path:
                        logger.info(f"[REQUEST:{request_id}] MCQ parsing completed successfully, JSON uploaded to: {json_s3_path}")
                    else:
                        logger.warning(f"[REQUEST:{request_id}] MCQ parsing failed or JSON upload failed")

                except Exception as e:
                    logger.error(f"[REQUEST:{request_id}] Error during streaming MCQ parsing: {e}")
                    logger.error(traceback.format_exc())
                    logger.warning(f"[REQUEST:{request_id}] Skipping MCQ parsing due to processing error")
                    json_s3_path = None
            else:
                logger.info(f"[REQUEST:{request_id}] Skipping MCQ parsing as total_questions not provided")

            # Step 7: Clean up local files
            self._cleanup_local_files(text_files + [combined_file_path])

            logger.info(f"[REQUEST:{request_id}] MCQ text extraction completed successfully")

            if self.delete_folder_by_id(book_id, chapter_id, res_id):
                logger.info(f"Successfully deleted resource folder for res_id: {res_id}")
            else:
                logger.warning(f"Failed to delete resource folder for res_id: {res_id}")

            # Prepare return response
            response = {
                "status": "success",
                "message": f"Successfully extracted text from {len(col_img_urls)} images",
                "s3_path": s3_upload_result,
                "total_images": len(col_img_urls),
                "text_files_created": len(text_files),
                "chapter_id": chapter_id,
                "resource_id": resource_id
            }

            # Add JSON processing information if it was performed
            if total_questions:
                response["mcq_parsing"] = {
                    "total_questions": total_questions,
                    "json_s3_path": json_s3_path,
                    "parsing_completed": json_s3_path is not None,
                    "api_processing_completed": json_s3_path is not None  # API processing happens if JSON was created successfully
                }

            return response

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error extracting text: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

        finally:
            # Final cleanup
            logger.info(f"[REQUEST:{request_id}] Extraction process completed")

    async def _extract_text_parallel(self, col_img_urls: List[str], request_id: str, resource_id: str, chapter_id: str, book_id: str) -> List[str]:
        """
        Extract text from images in parallel using ThreadPoolExecutor for true parallelism.

        Args:
            col_img_urls: List of column image URLs
            request_id: Request ID for logging
            resource_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID

        Returns:
            List[str]: List of created text file paths
        """
        # Create output directory for text files
        output_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id), "text_extraction")
        os.makedirs(output_dir, exist_ok=True)

        # Also ensure the source images directory exists
        source_images_dir = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id), str(resource_id))
        if not os.path.exists(source_images_dir):
            logger.error(f"[REQUEST:{request_id}] Source images directory does not exist: {source_images_dir}")
            return []

        logger.info(f"[REQUEST:{request_id}] Using output directory: {output_dir}")
        logger.info(f"[REQUEST:{request_id}] Source images directory: {source_images_dir}")

        def process_single_image_sync(args):
            """
            Synchronous function to process a single image in a thread.

            Args:
                args: Tuple of (index, image_url)

            Returns:
                str or None: Path to created text file, or None if failed
            """
            i, image_url = args
            try:
                # Parse page and column numbers from image URL
                page_number, col_number = self._parse_image_info(image_url)

                logger.info(f"[REQUEST:{request_id}] Extracting text from page {page_number}, column {col_number}")

                # Create extractor instance for this thread
                extractor = ExtractorAgent()

                # Create a new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                try:
                    # Run the extraction with a timeout
                    async def run_with_timeout():
                        return await asyncio.wait_for(
                            extractor._extract_with_prompt([image_url], mcq_text_extractor_prompt(), md_parse=False, md_result=False, llm_timeout=180),
                            timeout=getattr(config, 'EXTRACTION_TIMEOUT_SECONDS', 3000)
                        )

                    # Run the extraction in the current thread (blocking operation)
                    result, _ = loop.run_until_complete(run_with_timeout())

                    # Create text file name
                    text_file_name = f"page_{page_number}_col_{col_number}.txt"
                    text_file_path = os.path.join(output_dir, text_file_name)

                    # Save the extracted text to file
                    with open(text_file_path, "w", encoding="utf-8") as f:
                        f.write(result)

                    logger.info(f"[REQUEST:{request_id}] Successfully extracted text to {text_file_name}")
                    return text_file_path

                finally:
                    loop.close()

            except Exception as e:
                logger.error(f"[REQUEST:{request_id}] Error extracting text from page {page_number}, column {col_number}: {e}")
                return None

        # Create a list of arguments for each task
        task_args = [(i, image_url) for i, image_url in enumerate(col_img_urls)]

        logger.info(f"[REQUEST:{request_id}] Starting parallel text extraction for {len(task_args)} images using ThreadPoolExecutor")

        # Execute tasks in parallel using ThreadPoolExecutor
        from concurrent.futures import ThreadPoolExecutor, as_completed

        text_files = []
        failed_count = 0

        with ThreadPoolExecutor(max_workers=self.max_concurrent_extractions) as executor:
            # Submit all tasks and get future objects
            futures = []
            for arg in task_args:
                future = executor.submit(process_single_image_sync, arg)
                futures.append((future, arg))

            # Process results as they complete
            for future, arg in futures:
                try:
                    # Get the result from the future with a reasonable timeout
                    timeout_seconds = getattr(config, 'FUTURE_TIMEOUT_SECONDS', 1200)
                    result = future.result(timeout=timeout_seconds)

                    if result is not None:
                        text_files.append(result)
                    else:
                        failed_count += 1

                except Exception as exc:
                    logger.error(f"[REQUEST:{request_id}] Task for image {arg[0]+1} generated an exception: {exc}")
                    failed_count += 1

        logger.info(f"[REQUEST:{request_id}] Completed parallel text extraction. Created {len(text_files)} text files, {failed_count} failed")
        return text_files

    def _parse_image_info(self, image_url: str) -> tuple:
        """
        Parse page and column numbers from image URL.

        Args:
            image_url: Image URL containing page and column info

        Returns:
            tuple: (page_number, col_number)
        """
        try:
            # Extract filename from URL
            filename = os.path.basename(image_url)
            # Expected format: page_X_col_Y.png
            parts = filename.replace('.png', '').split('_')
            page_number = int(parts[1])
            col_number = int(parts[3])
            return page_number, col_number
        except Exception as e:
            logger.warning(f"Could not parse image info from {image_url}: {e}")
            return 1, 1  # Default values

    async def _merge_text_files(self, text_files: List[str], chapter_id: str, resource_id: str, request_id: str) -> Optional[str]:
        """
        Merge all text files into one combined file.

        Args:
            text_files: List of text file paths
            chapter_id: Chapter ID
            resource_id: Resource ID
            request_id: Request ID for logging

        Returns:
            Optional[str]: Path to the combined file, or None if failed
        """
        try:
            if not text_files:
                logger.error(f"[REQUEST:{request_id}] No text files provided for merging")
                return None

            # Filter out None values and check if files exist
            valid_files = []
            for text_file_path in text_files:
                if text_file_path and os.path.exists(text_file_path):
                    valid_files.append(text_file_path)
                else:
                    logger.warning(f"[REQUEST:{request_id}] Text file does not exist: {text_file_path}")

            if not valid_files:
                logger.error(f"[REQUEST:{request_id}] No valid text files found for merging")
                return None

            # Sort text files by page and column number
            sorted_files = sorted(valid_files, key=lambda x: self._get_sort_key(x))

            # Create combined file path
            combined_file_name = f"{chapter_id}_{resource_id}.txt"
            combined_file_path = os.path.join(os.path.dirname(sorted_files[0]), combined_file_name)

            logger.info(f"[REQUEST:{request_id}] Merging {len(sorted_files)} text files into {combined_file_name}")

            # Merge all text files
            with open(combined_file_path, "w", encoding="utf-8") as combined_file:
                for i, text_file_path in enumerate(sorted_files):
                    try:
                        with open(text_file_path, "r", encoding="utf-8") as f:
                            content = f.read().strip()
                            if content:
                                if i > 0:
                                    combined_file.write("\n\n")
                                combined_file.write(content)
                    except Exception as e:
                        logger.error(f"[REQUEST:{request_id}] Error reading text file {text_file_path}: {e}")
                        continue

            logger.info(f"[REQUEST:{request_id}] Successfully created combined file: {combined_file_name}")
            return combined_file_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error merging text files: {e}")
            logger.error(traceback.format_exc())
            return None

    def _get_sort_key(self, file_path: str) -> tuple:
        """
        Get sort key for text file based on page and column numbers.

        Args:
            file_path: Path to text file

        Returns:
            tuple: (page_number, col_number) for sorting
        """
        try:
            filename = os.path.basename(file_path)
            # Expected format: page_X_col_Y.txt
            parts = filename.replace('.txt', '').split('_')
            page_number = int(parts[1])
            col_number = int(parts[3])
            return page_number, col_number
        except Exception:
            return 999, 999  # Put unparseable files at the end

    def _cleanup_local_files(self, file_paths: List[str]):
        """
        Clean up local files.

        Args:
            file_paths: List of file paths to delete
        """
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.debug(f"Deleted local file: {file_path}")
            except Exception as e:
                logger.warning(f"Could not delete file {file_path}: {e}")

    async def _process_mcq_parsing_streaming(self, s3_text_path: str, total_questions: int, resource_id: str,
                                            chapter_id: str, book_id: str, request_id: str, combined_file_path: str, username: str, images_from_content: List[str]) -> Optional[str]:
        """
        Process MCQ parsing with streaming approach.

        Args:
            s3_text_path: S3 path to the combined text file
            total_questions: Total number of questions
            resource_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            request_id: Request ID for logging
            combined_file_path: Path to the combined text file
            username: Username of the user performing the extraction
            images_from_content: List of extracted image URLs to map to questions

        Returns:
            Optional[str]: S3 path of the final combined JSON file, or None if failed
        """
        try:
            logger.info(f"[REQUEST:{request_id}] Starting streaming MCQ parsing")

            # Use configurable batch size
            batch_size = self.mcq_batch_size
            num_batches = math.ceil(total_questions / batch_size)

            logger.info(f"[REQUEST:{request_id}] Processing {total_questions} questions in {num_batches} batches of {batch_size} (streaming mode)")

            # Create output directory for JSON files
            json_output_dir = os.path.join(os.path.dirname(combined_file_path), "json_batches")
            os.makedirs(json_output_dir, exist_ok=True)

            batch_json_files = []
            failed_batches = []

            # Process each batch
            for batch_num in range(1, num_batches + 1):
                start_question = (batch_num - 1) * batch_size + 1
                end_question = min(batch_num * batch_size, total_questions)

                logger.info(f"[REQUEST:{request_id}] Processing batch {batch_num}/{num_batches}: questions {start_question}-{end_question} (streaming)")

                # Call LLM for this batch using streaming approach
                batch_json_content = await self._call_llm_for_batch_streaming(
                    s3_text_path, start_question, end_question, request_id
                )

                if batch_json_content:
                    # Save batch JSON file
                    batch_json_filename = f"{resource_id}_{batch_num}.json"
                    batch_json_path = os.path.join(json_output_dir, batch_json_filename)

                    with open(batch_json_path, "w", encoding="utf-8") as f:
                        f.write(batch_json_content)

                    batch_json_files.append(batch_json_path)
                    logger.info(f"[REQUEST:{request_id}] Saved streaming batch {batch_num} JSON to {batch_json_filename}")
                    del batch_json_content
                else:
                    logger.error(f"[REQUEST:{request_id}] Failed to get JSON content for streaming batch {batch_num}, continuing with remaining batches")
                    failed_batches.append(batch_num)
                    continue

            # All streaming batches completed - check results
            successful_batches = len(batch_json_files)
            total_failed_batches = len(failed_batches)

            logger.info(f"[REQUEST:{request_id}] Streaming batch processing completed: {successful_batches} successful, {total_failed_batches} failed")

            if failed_batches:
                logger.warning(f"[REQUEST:{request_id}] Failed streaming batches: {failed_batches}")

            # Check if we have any successful batches to combine
            if not batch_json_files:
                logger.error(f"[REQUEST:{request_id}] No successful streaming batches to combine - all {num_batches} batches failed")
                return None

            logger.info(f"[REQUEST:{request_id}] Combining {successful_batches} successful streaming batch files")

            # Combine all batch JSON files into one
            combined_json_path = await self._combine_json_files(batch_json_files, resource_id, chapter_id, request_id, images_from_content)

            if not combined_json_path:
                logger.error(f"[REQUEST:{request_id}] Failed to combine JSON files")
                return None

            # Upload combined JSON to S3
            s3_json_path = await self._upload_combined_json_to_s3(
                combined_json_path, book_id, chapter_id, resource_id, request_id
            )

            # Process MCQs with external API after JSON is saved
            await self._process_mcqs_with_api(s3_json_path, chapter_id, resource_id, request_id, username)

            # Clean up batch JSON files
            self._cleanup_local_files(batch_json_files + [combined_json_path])

            logger.info(f"[REQUEST:{request_id}] Streaming MCQ parsing completed successfully")
            return s3_json_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in streaming MCQ parsing batches: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _process_mcq_parsing_batches(self, s3_text_path: str, total_questions: int, resource_id: str,
                                         chapter_id: str, book_id: str, request_id: str, combined_file_path: str, username: str, images_from_content: List[str]) -> Optional[str]:
        """
        Process MCQ parsing in batches and combine results.

        Args:
            s3_text_path: Combined text content from all extracted files
            total_questions: Total number of questions
            resource_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            request_id: Request ID for logging
            combined_file_path: Path to the combined text file
            username: Username of the user performing the extraction
            images_from_content: List of extracted image URLs to map to questions

        Returns:
            Optional[str]: S3 path of the final combined JSON file, or None if failed
        """
        try:
            logger.info(f"[REQUEST:{request_id}] Starting MCQ parsing")

            # Calculate batch size (10 questions per batch)
            batch_size = 10
            num_batches = math.ceil(total_questions / batch_size)

            logger.info(f"[REQUEST:{request_id}] Processing {total_questions} questions in {num_batches} batches of {batch_size}")

            # Create output directory for JSON files
            json_output_dir = os.path.join(os.path.dirname(combined_file_path), "json_batches")
            os.makedirs(json_output_dir, exist_ok=True)

            batch_json_files = []
            failed_batches = []

            content = read_file_from_s3(s3_text_path)
            if content is None:
                logger.error(f"[REQUEST:{request_id}] Failed to read text content from S3: {s3_text_path}")
                return None

            # Convert bytes to string
            text_content = content.decode("utf-8")
            # Process each batch
            for batch_num in range(1, num_batches + 1):
                start_question = (batch_num - 1) * batch_size + 1
                end_question = min(batch_num * batch_size, total_questions)

                logger.info(f"[REQUEST:{request_id}] Processing batch {batch_num}/{num_batches}: questions {start_question}-{end_question}")



                # Call LLM for this batch
                batch_json_content = await self._call_llm_for_batch(
                    text_content, start_question, end_question, request_id
                )

                if batch_json_content:
                    # Save batch JSON file
                    batch_json_filename = f"{resource_id}_{batch_num}.json"
                    batch_json_path = os.path.join(json_output_dir, batch_json_filename)

                    with open(batch_json_path, "w", encoding="utf-8") as f:
                        f.write(batch_json_content)

                    batch_json_files.append(batch_json_path)
                    logger.info(f"[REQUEST:{request_id}] Saved batch {batch_num} JSON to {batch_json_filename}")

                else:
                    logger.error(f"[REQUEST:{request_id}] Failed to get JSON content for batch {batch_num}, continuing with remaining batches")
                    failed_batches.append(batch_num)
                    continue

            # All batches completed - check results
            successful_batches = len(batch_json_files)
            total_failed_batches = len(failed_batches)

            logger.info(f"[REQUEST:{request_id}] Batch processing completed: {successful_batches} successful, {total_failed_batches} failed")

            if failed_batches:
                logger.warning(f"[REQUEST:{request_id}] Failed batches: {failed_batches}")

            # Check if we have any successful batches to combine
            if not batch_json_files:
                logger.error(f"[REQUEST:{request_id}] No successful batches to combine - all {num_batches} batches failed")
                return None

            logger.info(f"[REQUEST:{request_id}] Combining {successful_batches} successful batch files")

            # Combine all batch JSON files into one
            combined_json_path = await self._combine_json_files(batch_json_files, resource_id, chapter_id, request_id, images_from_content)
            if not combined_json_path:
                logger.error(f"[REQUEST:{request_id}] Failed to combine JSON files")
                return None

            # Upload combined JSON to S3
            s3_json_path = await self._upload_combined_json_to_s3(
                combined_json_path, book_id, chapter_id, resource_id, request_id
            )

            # Process MCQs with external API after JSON is saved
            await self._process_mcqs_with_api(s3_json_path, chapter_id, resource_id, request_id, username)

            # Clean up batch JSON files
            self._cleanup_local_files(batch_json_files + [combined_json_path])

            del text_content
            del content

            logger.info(f"[REQUEST:{request_id}] MCQ parsing completed successfully")
            return s3_json_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in MCQ parsing batches: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _call_llm_for_batch_streaming(self, s3_text_path: str, start_question: int, end_question: int, request_id: str) -> Optional[str]:
        """
        Call LLM to parse MCQs for a specific batch using streaming approach.

        Args:
            s3_text_path: S3 path to the combined text file
            start_question: Starting question number for this batch
            end_question: Ending question number for this batch
            request_id: Request ID for logging

        Returns:
            Optional[str]: JSON string response from LLM, or None if failed
        """
        try:
            # Read text content from S3
            from utils.s3_utils import read_file_from_s3

            logger.info(f"[REQUEST:{request_id}] Reading text content from S3 for batch {start_question}-{end_question}")

            # Read content from S3
            content = read_file_from_s3(s3_text_path)
            if content is None:
                logger.error(f"[REQUEST:{request_id}] Failed to read text content from S3: {s3_text_path}")
                return None

            # Convert bytes to string
            text_content = content.decode("utf-8")
            logger.info(f"[REQUEST:{request_id}] Successfully read {len(text_content)} characters from S3 for batch processing")

            # Get LLM with temperature 0 (default)
            llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=180)

            # Create the prompt using mcq_parser_prompt
            prompt_text = mcq_parser_prompt(start_question, end_question, text_content)

            logger.info(f"[REQUEST:{request_id}] Calling LLM for streaming batch questions {start_question}-{end_question}")

            # Call LLM
            response = llm.invoke([HumanMessage(content=prompt_text)])

            # Extract content from response
            json_content = response.content

            logger.info(f"[REQUEST:{request_id}] LLM response received for streaming batch questions {start_question}-{end_question}")

            return json_content

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error calling LLM for streaming batch {start_question}-{end_question}: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _call_llm_for_batch(self, text_content: str, start_question: int, end_question: int, request_id: str) -> Optional[str]:
        """
        Call LLM to parse MCQs for a specific batch.

        Args:
            text_content: Combined text content
            start_question: Starting question number for this batch
            end_question: Ending question number for this batch
            request_id: Request ID for logging

        Returns:
            Optional[str]: JSON string response from LLM, or None if failed
        """
        try:
            # Get LLM with temperature 0 (default)
            llm = llm_factory.get_llm("openai_admin", "gpt-4.1-mini", req_timeout=180)

            # Create the prompt using mcq_parser_prompt
            prompt_text = mcq_parser_prompt(start_question, end_question, text_content)

            logger.info(f"[REQUEST:{request_id}] Calling LLM for batch questions {start_question}-{end_question}")

            # Call LLM
            response = llm.invoke([HumanMessage(content=prompt_text)])

            # Extract content from response
            json_content = response.content

            logger.info(f"[REQUEST:{request_id}] LLM response received for batch questions {start_question}-{end_question}")

            return json_content

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error calling LLM for batch {start_question}-{end_question}: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _combine_json_files(self, batch_json_files: List[str], resource_id: str, chapter_id: str, request_id: str, images_from_content: List[str] = None) -> Optional[str]:
        """
        Combine all batch JSON files into one final JSON file.

        Args:
            batch_json_files: List of batch JSON file paths
            resource_id: Resource ID
            chapter_id: Chapter ID
            request_id: Request ID for logging
            images_from_content: List of extracted image URLs to map to questions

        Returns:
            Optional[str]: Path to the combined JSON file, or None if failed
        """
        try:
            combined_questions = []

            # Read and parse each batch JSON file
            for i, batch_file in enumerate(batch_json_files):
                try:
                    with open(batch_file, "r", encoding="utf-8") as f:
                        batch_content = f.read().strip()

                    # Parse JSON content
                    batch_json = json.loads(batch_content)

                    # Extract questions array
                    if "questions" in batch_json and isinstance(batch_json["questions"], list):
                        combined_questions.extend(batch_json["questions"])
                        logger.info(f"[REQUEST:{request_id}] Added {len(batch_json['questions'])} questions from {os.path.basename(batch_file)}")
                    else:
                        logger.warning(f"[REQUEST:{request_id}] No 'questions' array found in {os.path.basename(batch_file)}")

                except json.JSONDecodeError as e:
                    logger.error(f"[REQUEST:{request_id}] JSON decode error in {os.path.basename(batch_file)}: {e}")
                    continue
                except Exception as e:
                    logger.error(f"[REQUEST:{request_id}] Error reading {os.path.basename(batch_file)}: {e}")
                    continue

            # Map images to questions if images are provided
            if images_from_content:
                logger.info(f"[REQUEST:{request_id}] Mapping {len(images_from_content)} images to {len(combined_questions)} questions")
                combined_questions = self._map_images_to_questions(combined_questions, images_from_content, request_id)
            else:
                logger.info(f"[REQUEST:{request_id}] No images provided for mapping, keeping empty image arrays")
                # Ensure all questions have empty image arrays
                for question in combined_questions:
                    question['question_images'] = question.get('question_images', [])
                    question['option_images'] = question.get('option_images', [])
                    question['explanation_images'] = question.get('explanation_images', [])

            # Create final combined JSON structure
            final_json = {
                "questions": combined_questions
            }

            # Create combined JSON file path
            combined_json_filename = f"{chapter_id}_{resource_id}_mcqs.json"
            combined_json_path = os.path.join(os.path.dirname(batch_json_files[0]), combined_json_filename)

            # Write combined JSON file
            with open(combined_json_path, "w", encoding="utf-8") as f:
                json.dump(final_json, f, indent=2, ensure_ascii=False)

            logger.info(f"[REQUEST:{request_id}] Combined {len(combined_questions)} questions into {combined_json_filename}")

            return combined_json_path

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error combining JSON files: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _upload_combined_json_to_s3(self, combined_json_path: str, book_id: str, chapter_id: str,
                                        resource_id: str, request_id: str) -> Optional[str]:
        """
        Upload the combined JSON file to S3.

        Args:
            combined_json_path: Path to the combined JSON file
            book_id: Book ID
            chapter_id: Chapter ID
            resource_id: Resource ID
            request_id: Request ID for logging

        Returns:
            Optional[str]: S3 path of the uploaded file, or None if failed
        """
        try:
            # Upload combined JSON file to S3
            json_filename = f"{chapter_id}_{resource_id}_mcqs.json"
            s3_upload_result = upload_file_to_s3(
                local_file_path=combined_json_path,
                book_id=book_id,
                chapter_id=chapter_id,
                res_id=resource_id,
                file_name=json_filename,
                is_quiz_image=False
            )

            if s3_upload_result:
                logger.info(f"[REQUEST:{request_id}] Successfully uploaded combined JSON to S3: {s3_upload_result}")
                return s3_upload_result
            else:
                logger.error(f"[REQUEST:{request_id}] Failed to upload combined JSON to S3")
                return None

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error uploading combined JSON to S3: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _process_mcqs_with_api(self, json_s3_path: str, chapter_id: str, resource_id: str,
                                   request_id: str, username: str) -> None:
        """
        Process MCQs with external API using the process_mcqs method from MCQExtractor.

        Args:
            json_s3_path: S3 path to the combined JSON file
            chapter_id: Chapter ID
            resource_id: Resource ID
            request_id: Request ID for logging
            username: Username of the user performing the extraction
        """
        try:
            logger.info(f"[REQUEST:{request_id}] Starting MCQ processing with external API")

            # Read the combined JSON file from S3
            from utils.s3_utils import read_file_from_s3, get_s3_path

            # Get the full S3 path for the JSON file
            full_s3_path = get_s3_path(json_s3_path)
            logger.info(f"[REQUEST:{request_id}] Reading combined JSON from S3: {full_s3_path}")

            # Read content from S3
            content = read_file_from_s3(full_s3_path)
            if content is None:
                logger.error(f"[REQUEST:{request_id}] Failed to read JSON file from S3: {full_s3_path}")
                return

            # Convert bytes to string and parse JSON
            content_str = content.decode("utf-8")
            json_content = json.loads(content_str)
            logger.info(f"[REQUEST:{request_id}] Successfully read and parsed JSON from S3")

            # Extract the questions array
            questions = json_content.get("questions", [])

            if not questions:
                logger.warning(f"[REQUEST:{request_id}] No questions found in combined JSON file")
                return

            logger.info(f"[REQUEST:{request_id}] Found {len(questions)} questions to process")

            # Import and create MCQExtractor instance
            from agents.mcq_extractor import MCQExtractor
            mcq_extractor = MCQExtractor()

            # Call the process_mcqs method
            processed_mcqs = await mcq_extractor.process_mcqs(questions, chapter_id, resource_id, username)

            logger.info(f"[REQUEST:{request_id}] Successfully processed {len(processed_mcqs)} MCQs with external API")

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error processing MCQs with external API: {e}")
            logger.error(traceback.format_exc())

    async def get_extracted_text(self, chapter_id: str, res_id: str) -> Dict:
        """
        Read the extracted MCQ text for a resource using the same pattern as PDF text extractor.

        Args:
            chapter_id: Chapter ID
            res_id: Resource ID

        Returns:
            Dict: Text content or error message
        """
        try:
            # Get a database session for the wscontent schema
            db_session = next(get_session(CONTENT_SCHEMA))
            try:
                # Query the resource_dtl table to get resource details
                resource_query = text("""
                    SELECT id, chapter_id, extract_path
                    FROM wscontent.resource_dtl
                    WHERE id = :res_id
                    LIMIT 1
                """)

                resource_result = db_session.execute(resource_query, {"res_id": res_id})
                resource_row = resource_result.fetchone()

                if not resource_row:
                    logger.error(f"Resource with ID {res_id} not found in database")
                    return {"status": "error", "message": f"Resource with ID {res_id} not found"}

                # Extract resource details
                resource_id = resource_row[0]
                chapter_id_db = resource_row[1]
                extract_path = resource_row[2]

                logger.info(f"Found resource ID: {resource_id}, chapter ID: {chapter_id_db}, extract path: {extract_path}")

                # Query the chapters_mst table to get the book_id
                chapter_query = text("""
                    SELECT book_id
                    FROM wscontent.chapters_mst
                    WHERE id = :chapter_id
                    LIMIT 1
                """)

                chapter_result = db_session.execute(chapter_query, {"chapter_id": chapter_id_db})
                chapter_row = chapter_result.fetchone()

                if not chapter_row:
                    logger.error(f"Chapter with ID {chapter_id_db} not found in database")
                    return {"status": "error", "message": f"Chapter with ID {chapter_id_db} not found"}

                # Extract book_id
                book_id = chapter_row[0]
                logger.info(f"Found book ID: {book_id}")

                # First try to read directly from the extract_path if it exists
                if extract_path:
                    try:
                        from utils.s3_utils import read_file_from_s3, get_s3_path

                        # Try direct path first
                        direct_s3_path = get_s3_path(extract_path)
                        logger.info(f"Trying to read from direct S3 path: {direct_s3_path}")
                        content = read_file_from_s3(direct_s3_path)

                        if content is not None:
                            # Convert bytes to string
                            content_str = content.decode("utf-8")
                            logger.info(f"Successfully read {len(content_str)} characters from direct S3 path")
                            db_session.close()
                            logger.debug("Database session closed after finding content at direct S3 path")
                            return {"status": "success", "content": content_str, "source": "s3_direct", "s3_path": direct_s3_path}
                        else:
                            logger.warning(f"Could not read from direct S3 path: {direct_s3_path}")
                    except Exception as e:
                        logger.warning(f"Error reading from direct S3 path: {e}")

                # Try to construct the S3 path based on the expected MCQ text extraction structure
                try:
                    from utils.s3_utils import read_file_from_s3, get_s3_path

                    # For MCQ text extraction, files go in extractedImages folder with format: {chapter_id}_{res_id}.txt
                    constructed_s3_path = f"supload/pdfextracts/{book_id}/{chapter_id_db}/{resource_id}/extractedImages/{chapter_id_db}_{resource_id}.txt"
                    full_s3_path = get_s3_path(constructed_s3_path)
                    logger.info(f"Trying constructed MCQ text S3 path: {full_s3_path}")

                    content = read_file_from_s3(full_s3_path)
                    if content is not None:
                        # Convert bytes to string
                        content_str = content.decode("utf-8")
                        logger.info(f"Successfully read {len(content_str)} characters from constructed S3 path")

                        # Update the database with the correct path if it wasn't set
                        if not extract_path:
                            try:
                                from utils.db_utils import update_extract_path
                                # Use the utility function to update the database
                                success = update_extract_path(res_id, constructed_s3_path, CONTENT_SCHEMA)

                                if success:
                                    logger.info(f"Updated database with correct extract_path: {constructed_s3_path}")
                                else:
                                    logger.warning(f"Failed to update database with correct path: {constructed_s3_path}")
                            except Exception as update_err:
                                logger.error(f"Failed to update database with correct path: {update_err}")
                                logger.error(traceback.format_exc())

                        db_session.close()
                        logger.debug("Database session closed after finding content at constructed S3 path")
                        return {"status": "success", "content": content_str, "source": "s3_constructed", "s3_path": full_s3_path}
                    else:
                        logger.warning(f"Could not read from constructed S3 path: {full_s3_path}")
                except Exception as e:
                    logger.warning(f"Error reading from constructed S3 path: {e}")

                # If we've reached here, we couldn't find the file
                logger.error(f"MCQ extracted text file not found for resource ID {res_id}")
                return {"status": "error", "message": f"MCQ extracted text file not found for resource with ID {res_id}"}
            finally:
                # Close the database session if it's still active
                if hasattr(db_session, 'is_active') and db_session.is_active:
                    db_session.close()
                    logger.debug("Database session closed at the end of get_extracted_text")

        except Exception as e:
            logger.error(f"Error reading MCQ extracted text: {e}")
            logger.error(traceback.format_exc())
            return {"status": "error", "message": str(e)}

    def get_quiz_images(self, res_id: str, chapter_id: str, book_id: str, image_urls, explanation_start_page: int = 5) -> List[str]:
        """
        Extract quiz images from PDF pages and return the list of extracted image URLs.

        Args:
            res_id: Resource ID
            chapter_id: Chapter ID
            book_id: Book ID
            image_urls: List of column image URLs
            explanation_start_page: Page number from which explanations start (default: 5)

        Returns:
            List[str]: List of extracted quiz image URLs
        """
        from agents.core.extractor import ExtractorAgent
        img_extractor = ExtractorAgent()
        all_extracted_img_urls = []
        start_idx = 0

        for idx in range(start_idx, len(image_urls)):
            col_img_url = image_urls[idx]
            page_name = col_img_url.split('/')[-1]
            page_id = page_name.split('.')[0]

            mcq_full_img_storage_path = os.path.join(config.PDF_PAGE_IMG_OUTPUT_DIR, str(book_id), str(chapter_id),
                                                     str(res_id))

            # Check if the directory exists
            if not os.path.exists(mcq_full_img_storage_path):
                logger.warning(f"MCQ image storage path does not exist: {mcq_full_img_storage_path}")
                continue

            mcq_local_image_paths = [
                os.path.join(mcq_full_img_storage_path, img)
                for img in os.listdir(mcq_full_img_storage_path)
                if img.lower().endswith(f"{page_id}.png")
            ]
            mcq_local_image_paths.sort(key=lambda x: self.natural_sort_key(os.path.basename(x)))
            logger.debug(f"Found {len(mcq_local_image_paths)} local image paths for page {page_id}")

            if mcq_local_image_paths:
                logger.info(f"Extracting mcq images for page {page_id}...")
                page_number = int(page_id.split('_')[1])

                # Convert explanation_start_page to int if it's a string
                if isinstance(explanation_start_page, str):
                    try:
                        explanation_start_page = int(explanation_start_page)
                    except ValueError:
                        logger.warning(f"Invalid explanation_start_page value: {explanation_start_page}, using default value 5")
                        explanation_start_page = 5

                if page_number >= explanation_start_page:
                    prompt = pre_process_explanation_prompt
                else:
                    prompt = pre_process_prompt
                prompt_content = img_extractor.construct_input_content(prompt, mcq_local_image_paths)

                # Ensure LLM is initialized before use
                self._ensure_llm_initialized()
                response = self.llm.invoke([HumanMessage(content=prompt_content, additional_kwargs={"tool_choice": "vision"})])

                try:
                    # Try JSON load first
                    parsed = json.loads(response.content)
                except json.JSONDecodeError:
                    # If it fails, try ast.literal_eval
                    parsed = ast.literal_eval(response.content)

                mappings = parsed.get("mappings", [])
                explanation_mappings = parsed.get("explanationMappings", [])

                extracted_img_urls = img_extractor.extract_quiz_images_new(mcq_local_image_paths, book_id, chapter_id, res_id, mappings, explanation_mappings)
                if extracted_img_urls:
                    all_extracted_img_urls = extracted_img_urls
                    logger.info(f"Extracted {len(extracted_img_urls)} images for page {page_id}")

        logger.info(f"Total extracted quiz images: {len(all_extracted_img_urls)}")
        return all_extracted_img_urls

    def _map_images_to_questions(self, questions: List[Dict], images_from_content: List[str], request_id: str) -> List[Dict]:
        """
        Map extracted images to their corresponding questions based on question numbers.

        Supports the following image filename patterns:
        - question_X.png: Question image for question X
        - question_X_option_Y.png: Option image Y for question X
        - question_X_explanation.png: Explanation image for question X
        - explanation_X.png: Standalone explanation image for question X

        Args:
            questions: List of question dictionaries
            images_from_content: List of extracted image URLs
            request_id: Request ID for logging

        Returns:
            List[Dict]: Updated questions with mapped images
        """
        try:
            logger.info(f"[REQUEST:{request_id}] Starting image mapping for {len(questions)} questions and {len(images_from_content)} images")

            # Create a mapping of question numbers to images
            question_image_map = {}

            for image_url in images_from_content:
                try:
                    # Extract filename from URL
                    filename = os.path.basename(image_url)
                    logger.debug(f"Processing image: {filename}")

                    if filename.startswith('question_'):
                        # Remove extension and split by underscore
                        name_parts = filename.replace('.png', '').split('_')

                        if len(name_parts) >= 2:
                            # Extract question number (handle both int and string)
                            try:
                                question_num = int(name_parts[1])
                            except ValueError:
                                # Handle string question numbers or special cases
                                question_num_str = name_parts[1]
                                import re
                                numbers = re.findall(r'\d+', question_num_str)
                                if numbers:
                                    question_num = int(numbers[0])
                                else:
                                    logger.warning(f"Could not extract question number from {filename}")
                                    continue

                            # Initialize question mapping if not exists
                            if question_num not in question_image_map:
                                question_image_map[question_num] = {
                                    'question_images': [],
                                    'option_images': [],
                                    'explanation_images': []
                                }

                            # Determine image type and add to appropriate array
                            if len(name_parts) == 2:
                                # question_X.png - question image
                                question_image_map[question_num]['question_images'].append(image_url)
                                logger.debug(f"Added question image for Q{question_num}: {filename}")
                            elif len(name_parts) >= 3:
                                if name_parts[2] == 'option' and len(name_parts) >= 4:
                                    # question_X_option_Y.png - option image
                                    question_image_map[question_num]['option_images'].append(image_url)
                                    logger.debug(f"Added option image for Q{question_num}: {filename}")
                                elif name_parts[2] == 'explanation':
                                    # question_X_explanation.png - explanation image
                                    question_image_map[question_num]['explanation_images'].append(image_url)
                                    logger.debug(f"Added explanation image for Q{question_num}: {filename}")
                                else:
                                    logger.debug(f"Unknown image type for {filename}, treating as question image")
                                    question_image_map[question_num]['question_images'].append(image_url)
                        else:
                            logger.warning(f"Invalid filename format: {filename}")

                    elif filename.startswith('explanation_'):
                        name_parts = filename.replace('.png', '').split('_')

                        if len(name_parts) >= 2:
                            # Extract explanation number (handle both int and string)
                            try:
                                explanation_num = int(name_parts[1])
                            except ValueError:
                                # Handle string explanation numbers or special cases
                                explanation_num_str = name_parts[1]
                                import re
                                numbers = re.findall(r'\d+', explanation_num_str)
                                if numbers:
                                    explanation_num = int(numbers[0])
                                else:
                                    logger.warning(f"Could not extract explanation number from {filename}")
                                    continue

                            # For standalone explanation images, map them to the corresponding question number
                            # Assuming explanation_X.png corresponds to question X
                            question_num = explanation_num

                            # Initialize question mapping if not exists
                            if question_num not in question_image_map:
                                question_image_map[question_num] = {
                                    'question_images': [],
                                    'option_images': [],
                                    'explanation_images': []
                                }

                            # Add to explanation images for the corresponding question
                            question_image_map[question_num]['explanation_images'].append(image_url)
                            logger.debug(f"Added standalone explanation image for Q{question_num}: {filename}")
                        else:
                            logger.warning(f"Invalid explanation filename format: {filename}")

                    else:
                        logger.debug(f"Skipping unrecognized image: {filename}")

                except Exception as e:
                    logger.error(f"Error processing image {image_url}: {e}")
                    continue

            logger.info(f"[REQUEST:{request_id}] Created image mapping for {len(question_image_map)} questions")

            # Map images to questions
            updated_questions = []
            for question in questions:
                try:
                    question_number = question.get('question_number')
                    if question_number is not None:
                        # Handle both int and string question numbers
                        try:
                            q_num = int(question_number)
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid question number format: {question_number}")
                            # Keep original question without images
                            updated_questions.append(question)
                            continue

                        # Get images for this question
                        if q_num in question_image_map:
                            question['question_images'] = question_image_map[q_num]['question_images']
                            question['option_images'] = question_image_map[q_num]['option_images']
                            question['explanation_images'] = question_image_map[q_num]['explanation_images']

                            total_images = (len(question['question_images']) +
                                          len(question['option_images']) +
                                          len(question['explanation_images']))
                            if total_images > 0:
                                logger.debug(f"Mapped {total_images} images to question {q_num}")
                        else:
                            # No images found for this question, keep empty arrays
                            question['question_images'] = question.get('question_images', [])
                            question['option_images'] = question.get('option_images', [])
                            question['explanation_images'] = question.get('explanation_images', [])
                    else:
                        logger.warning(f"Question missing question_number field")
                        # Keep original question without images
                        question['question_images'] = question.get('question_images', [])
                        question['option_images'] = question.get('option_images', [])
                        question['explanation_images'] = question.get('explanation_images', [])

                    updated_questions.append(question)

                except Exception as e:
                    logger.error(f"Error mapping images to question: {e}")
                    # Keep original question without images
                    question['question_images'] = question.get('question_images', [])
                    question['option_images'] = question.get('option_images', [])
                    question['explanation_images'] = question.get('explanation_images', [])
                    updated_questions.append(question)

            # Log summary
            total_mapped_images = sum(
                len(q.get('question_images', [])) +
                len(q.get('option_images', [])) +
                len(q.get('explanation_images', []))
                for q in updated_questions
            )
            logger.info(f"[REQUEST:{request_id}] Image mapping completed. Total images mapped: {total_mapped_images}")

            return updated_questions

        except Exception as e:
            logger.error(f"[REQUEST:{request_id}] Error in image mapping: {e}")
            logger.error(traceback.format_exc())
            # Return original questions with empty image arrays
            for question in questions:
                question['question_images'] = question.get('question_images', [])
                question['option_images'] = question.get('option_images', [])
                question['explanation_images'] = question.get('explanation_images', [])
            return questions

    def natural_sort_key(self, s):
        # Extract the number from the filename using regex
        numbers = re.findall(r'(\d+)', s)
        if numbers:
            return int(numbers[0])
        return s

    def delete_folder_by_id_extracts(self, book_id, chapter_id=None, res_id=None):
        """
        Delete a folder by ID from the extracted_text directory

        Parameters:
        book_id (str): The book ID
        chapter_id (str, optional): The chapter ID. If provided, deletes extracted_text/book_id/chapter_id path
        res_id (str, optional): The resource ID. If provided, deletes extracted_text/book_id/chapter_id/res_id path

        Returns:
        bool: True if deletion was successful, False otherwise
        """
        try:
            base_path = Path(config.PDF_PAGE_IMG_OUTPUT_DIR) / 'extracted_text'

            # Determine the path to delete based on provided parameters
            if res_id is not None and chapter_id is not None:
                # Delete specific resource folder: extracted_text/book_id/chapter_id/res_id
                folder_path = base_path / str(book_id) / str(chapter_id) / str(res_id)
                logger.info(f"Deleting extracted text resource folder: {folder_path}")
            elif chapter_id is not None:
                # Delete chapter folder: extracted_text/book_id/chapter_id
                folder_path = base_path / str(book_id) / str(chapter_id)
                logger.info(f"Deleting extracted text chapter folder: {folder_path}")
            else:
                # Delete book folder: extracted_text/book_id
                folder_path = base_path / str(book_id)
                logger.info(f"Deleting extracted text book folder: {folder_path}")

            if not folder_path.exists():
                logger.info(f"Folder {folder_path} does not exist")
                return False

            shutil.rmtree(folder_path)
            logger.info(f"Successfully deleted folder: {folder_path}")
            return True
        except Exception as e:
            logger.info(f"Error deleting extracted text folder: {str(e)}")
            return False

    def delete_folder_by_id(self, book_id, chapter_id=None, res_id=None):
        """
        Delete a folder by ID from the local_page_images directory

        Parameters:
        book_id (str): The book ID
        chapter_id (str, optional): The chapter ID. If provided, deletes book_id/chapter_id path
        res_id (str, optional): The resource ID. If provided, deletes book_id/chapter_id/res_id path

        Returns:
        bool: True if deletion was successful, False otherwise
        """
        try:
            base_path = Path(config.PDF_PAGE_IMG_OUTPUT_DIR)

            # Determine the path to delete based on provided parameters
            if res_id is not None and chapter_id is not None:
                # Delete specific resource folder: book_id/chapter_id/res_id
                folder_path = base_path / str(book_id) / str(chapter_id) / str(res_id)
                logger.info(f"Deleting resource folder: {folder_path}")
            elif chapter_id is not None:
                # Delete chapter folder: book_id/chapter_id
                folder_path = base_path / str(book_id) / str(chapter_id)
                logger.info(f"Deleting chapter folder: {folder_path}")
            else:
                # Delete book folder: book_id
                folder_path = base_path / str(book_id)
                logger.info(f"Deleting book folder: {folder_path}")

            if not folder_path.exists():
                logger.info(f"Folder {folder_path} does not exist")
                return False

            shutil.rmtree(folder_path)
            logger.info(f"Successfully deleted folder: {folder_path}")
            return True
        except Exception as e:
            logger.info(f"Error deleting folder: {str(e)}")
            return False
